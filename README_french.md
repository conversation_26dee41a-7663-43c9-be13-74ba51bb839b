# App Multi-Lingo - Édition Tamazight (Tamazirt)
![Démonstration de l'application](assets/images/image-1-tamazight-eyes-intro1280x720px-no-bg.gif)

Code Open-Source Github de l'App Multi-Lingo Tamazight :
https://github.com/mindful-ai-dude/v3.6-Tamazight_MultiLingo_App

Cartes de Modèles et Jeux de Données Hugging Face de l'App Multi-Lingo Tamazight :
https://huggingface.co/tamazightdev

## Rapprocher les langues, préserver la culture, renforcer la communication d'urgence.

*"En temps de crise, chaque mot compte. Pour la préservation, chaque langue est essentielle."*

### 🌍 Mission : Combler les lacunes de communication en temps de crise

Cette application répond à un besoin humanitaire critique identifié lors du tremblement de terre dévastateur de 2023 au Maroc, où les secouristes et les officiels ont eu du mal à communiquer efficacement avec les communautés berbères marocaines. Notre solution fournit une traduction instantanée, hors ligne et alimentée par l'IA pour garantir que personne ne soit laissé pour compte en cas d'urgence.

Une belle application mobile de traduction, prête pour la production, développée avec Expo et React Native, dotée de capacités de traduction IA hors ligne sur l'appareil et en ligne pour le tamazight (ⵜⴰⵎⴰⵣⵉⵖⵜ), prononcé "Tamazirt", la langue berbère autochtone du Maroc. L'application offre une traduction multidirectionnelle fluide entre le tamazight, l'arabe, le français et l'anglais, avec des fonctionnalités spécialisées pour les situations d'urgence, l'usage gouvernemental/parlementaire et comme outil de traduction multilingue à usage général.

## À propos de moi, Gregory Kennedy, le créateur et développeur de l'application :
- Je suis un ancien ingénieur de la Silicon Valley et un cinéaste primé qui a eu le grand honneur et le plaisir de voyager à travers le monde et de travailler avec des lauréats du prix Nobel de la paix (Adolfo Pérez Esquivel et Mairead Corrigan Maguire), l'experte en chimpanzés Dr. Jane Goodall en Afrique et en Europe, et avec le maître zen Thich Nhat Hanh en France, en Allemagne et aux États-Unis. J'ai été invité à prendre la parole devant des auditoires aux Nations Unies à Vienne, en Autriche et à New York, ainsi qu'à l'Université de Stanford.

- Je suis un citoyen américain dont la famille vit aux États-Unis depuis plus de 400 ans, et dont l'ascendance est le reflet de l'histoire américaine, composée de deux tribus amérindiennes (Itakapa et Blackfoot), ainsi que d'origines africaine, irlandaise, française et mexicaine.

LinkedIn : Pour plus d'informations sur moi, veuillez visiter mon profil LinkedIn :
https://www.linkedin.com/in/gregorykennedymindfuldude/

IMDB : Film primé The 5 Powers :
https://www.imdb.com/title/tt2771174/?ref_=mv_desc

## 🙏 Remerciements

- **Mon incroyable mère Valerie, décédée il y a quelques mois** : Qui m'a appris à me soucier des autres êtres humains, à explorer le monde et à traiter tout le monde avec respect et dignité.

![Gregory Kennedy et sa mère Valerie chez Ben and Jerry's à San Francisco, Haight Ashbury, avril 2016](assets/images/Gregory%20Kennedy%20and%20mom%20Valerie%20at%20Ben%20and%20Jerry's%20San%20Francisco%20Haight%20Ashbury%20April%202016.jpg)
Gregory et sa mère chez Ben & Jerry's à San Francisco :

- **Mes coéquipiers marocains** : Omar (Berbère marocain), Hamza et Ziyad pour leur dévouement et leur travail acharné. Merci et je vous suis reconnaissant !

- **Victimes du tremblement de terre de 2023 au Maroc** : Cette application rend hommage aux personnes touchées et vise à prévenir de futurs obstacles à la communication.

- **Communautés berbères marocaines** : Pour avoir préservé la langue et la culture tamazight à travers les siècles.

- **Le Royaume du Maroc** : Pour avoir adopté des lois visant à préserver et à honorer la langue et la culture tamazight, pour avoir fait du tamazight une langue officielle du Maroc et pour avoir rendu le tamazight obligatoire dans les écoles.

- **Les intervenants d'urgence** : Qui ont inspiré cette solution par leur dévouement pendant la crise.

- **Kaggle et Google DeepMind** : Pour le modèle Gemma-3n et Kaggle pour l'opportunité du hackathon.

- **L'équipe Unsloth (Discord) et les amis développeurs** : Pour leur soutien et leurs conseils incroyables.

## 🏆 Hackathon Kaggle Google Deep Mind

### **Détails de la compétition**
- **Événement** : [Hackathon Kaggle Google Deep Mind](https://www.kaggle.com/competitions/google-gemma-3n-hackathon)
- **Période** : Juillet 2025 - 6 août 2025
- **Modèle** : Modèle Gemma-3n avec 2 milliards de paramètres (sorti en juin 2025)
- **Objectif** : Applications d'IA humanitaire utilisant le dernier modèle Gemma-3n de Google avec 2 milliards de paramètres (utilisation hors ligne) et l'API Gemma-3 12b (utilisation en ligne) pour des applications humanitaires des grands modèles de langage.

### **Réalisations techniques**
- **Jeu de données pour l'ajustement fin** : Plus de 100 000 paires de langues en arabe, anglais, français et tifinagh.
- **Architecture double** : API en ligne + inférence hors ligne sur l'appareil.
- **Optimisation pour les urgences** : Entraînement spécialisé pour les scénarios de communication de crise.
- **Précision culturelle** : Intégration de l'expertise linguistique berbère marocaine.

![Capture d'écran de l'application 2](assets/images/image-3-v3.6-Tamazight_MultiLingo_App-main-web.png)

### **Points forts de l'innovation**
- **Première IA pour le tamazight** : Pionnier de la traduction par IA pour les langues berbères autochtones.
- **Conception axée sur l'urgence** : Priorise les cas d'usage humanitaires par rapport à la traduction générale.
- **Conformité constitutionnelle** : Soutient la reconnaissance de la langue officielle du Maroc.
- **Fiabilité hors ligne** : Essentielle dans les scénarios de catastrophe avec des infrastructures endommagées.
- **Préservation culturelle** : Promeut une langue menacée grâce à la technologie moderne.

### **Indicateurs d'impact**
- **Utilisateurs cibles** : Intervenants d'urgence, communautés berbères, fonctionnaires gouvernementaux.
- **Cas d'usage** : Secours post-séisme, urgences médicales, procédures judiciaires.
- **Langues préservées** : Variantes du tamazight (tachelhit, tarifit, tamazight du centre de l'Atlas).
- **Phrases d'urgence** : Plus de 50 phrases de communication critiques préchargées.

## 🌟 Fonctionnalités

### 🚨 Conception axée sur l'urgence
- **Communication de crise** : Spécialement conçue pour les scénarios de tremblement de terre et de secours en cas de catastrophe.
- **Priorité au hors ligne** : Fonctionne sans connexion Internet lorsque les infrastructures sont endommagées.
- **Phrases d'urgence** : Phrases critiques préchargées pour les situations médicales et de sauvetage.
- **Accès instantané** : Aucune configuration requise - fonctionne immédiatement pour les intervenants d'urgence.

### 🤖 Traduction IA avancée
- **Traduction bi-mode** :
  - **En ligne** : Dernière API Gemma-3 12b pour une précision maximale.
  - **Hors ligne** : Modèle Gemma-3n 2b ajusté sur l'appareil pour la confidentialité et la fiabilité.
- **Traduction multidirectionnelle** : Arabe ↔ Tamazight, Français ↔ Tamazight, Anglais ↔ Tamazight.
- **Conscience du contexte** : Contextes d'urgence, gouvernementaux et généraux avec une terminologie spécialisée.
- **Précision culturelle** : Ajusté sur plus de 100 000 paires de langues marocaines.

### 🔤 Prise en charge de la langue tamazight
- **Alphabet Tifinagh** : Prise en charge native du système d'écriture ⵜⵉⴼⵉⵏⴰⵖ.
- **Focalisation sur le Tachelhit** : Version initiale optimisée pour la variante tachelhit du tamazight.
- **Reconnaissance constitutionnelle** : Aligné sur l'article 5 de la Constitution marocaine de 2011.
- **Préservation culturelle** : Promeut et préserve le patrimoine berbère autochtone.

### 📱 Expérience utilisateur
- **Interface utilisateur magnifique en "glass-morphism"** : Interface moderne et élégante avec des arrière-plans en dégradé.
- **Intégration vocale** : Saisie de la parole au texte et sortie du texte à la parole avec audio natif.
- **Historique des traductions** : Base de données SQLite hors ligne sur l'appareil (base de données temps réel Convex en ligne) avec favoris et fonctionnalité de recherche.
- **Conception réactive** : S'adapte parfaitement à toutes les tailles d'écran avec une gestion correcte de la zone de sécurité.
- **Retour haptique** : Réponses tactiles pour une meilleure interaction utilisateur (mobile uniquement).

## 🚀 Installation

### Prérequis
- Node.js 18+
- Gestionnaire de paquets npm ou pnpm
- Expo CLI (optionnel, pour des fonctionnalités de développement supplémentaires)

### Utilisation de npm

```bash
# Cloner le dépôt
git clone <url-du-depot>
cd tamazight-translate

# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev
```

### Utilisation de pnpm

```bash
# Cloner le dépôt
git clone <url-du-depot>
cd tamazight-translate

# Installer les dépendances
pnpm install

# Démarrer le serveur de développement
pnpm dev
```

### Serveur de développement

Après avoir exécuté la commande de développement, vous verrez :

```
Starting project at /home/<USER>
› Metro waiting on exp://192.168.1.100:8081
› Scan the QR code above with Expo Go (Android) or the Camera app (iOS)
› Press a │ open Android
› Press i │ open iOS simulator
› Press w │ open web
```

- **Web** : Appuyez sur `w` pour ouvrir dans votre navigateur.
- **Mobile** : Scannez le code QR avec l'application Expo Go.
- **Simulateur iOS** : Appuyez sur `i` (nécessite Xcode).
- **Émulateur Android** : Appuyez sur `a` (nécessite Android Studio).

## 📱 Structure de l'application et flux utilisateur

### Architecture de navigation

L'application utilise une structure de navigation par onglets avec quatre sections principales :

#### 1. **Onglet Traduire** (Accueil)
- **Objectif** : Interface de traduction principale.
- **Fonctionnalités** :
  - Sélecteur de langue avec fonction d'inversion.
  - Saisie de texte avec prise en charge du clavier tifinagh.
  - Commandes d'entrée/sortie vocale.
  - Bouton de traduction par caméra.
  - Traduction IA en temps réel.
  - Sauvegarde de l'historique des traductions.

**Flux utilisateur** :
1. Sélectionner les langues source et cible.
2. Saisir le texte en tapant, par la voix ou via la caméra.
3. Appuyer sur traduire pour une traduction alimentée par l'IA.
4. Utiliser la sortie vocale pour entendre la prononciation.
5. Enregistrer dans les favoris ou l'historique.

#### 2. **Onglet Historique**
- **Objectif** : Gérer l'historique des traductions et les favoris.
- **Fonctionnalités** :
  - Rechercher dans les traductions passées.
  - Filtrer par favoris.
  - Supprimer les entrées non désirées.
  - Re-traduire ou modifier des entrées précédentes.

**Flux utilisateur** :
1. Parcourir l'historique chronologique des traductions.
2. Rechercher des traductions spécifiques.
3. Activer/désactiver le filtre des favoris.
4. Appuyer sur un élément pour entendre la prononciation.
5. Balayer ou appuyer pour supprimer des entrées.

#### 3. **Onglet Urgence**
- **Objectif** : Accès rapide aux phrases critiques.
- **Fonctionnalités** :
  - Phrases d'urgence préchargées.
  - Catégories médicales, police et besoins de base.
  - Phrases codées par priorité (haute/moyenne/basse).
  - Sortie vocale instantanée.
  - Coordonnées d'urgence du Maroc.

**Flux utilisateur** :
1. Sélectionner la catégorie d'urgence (Médical, Urgence, Besoins de base).
2. Choisir la langue cible.
3. Appuyer sur une phrase pour une sortie vocale instantanée.
4. Accéder aux numéros d'urgence du Maroc (15, 19, 177).

#### 4. **Onglet Gouvernement**
- **Objectif** : Terminologie officielle et parlementaire.
- **Fonctionnalités** :
  - Phrases de procédure parlementaire.
  - Termes juridiques et administratifs.
  - Informations sur les droits constitutionnels.
  - Terminologie du système éducatif.
  - Phrases des services publics.

**Flux utilisateur** :
1. Sélectionner la catégorie (Parlement, Juridique, Administratif, etc.).
2. Choisir la langue cible.
3. Parcourir les phrases spécifiques au contexte.
4. S'informer sur les droits linguistiques au Maroc.

### Interactions utilisateur clés

#### Sélection de la langue
- Appuyer sur les boutons de langue pour ouvrir la modale de sélection.
- Utiliser le bouton d'inversion pour inverser rapidement le sens de la traduction.
- Retour visuel avec des animations fluides.

#### Méthodes de saisie de texte
1. **Clavier** : Saisie de texte standard.
2. **Clavier Tifinagh** : Activer/désactiver le clavier virtuel tifinagh.
3. **Saisie vocale** : Appuyer sur le microphone pour la reconnaissance vocale.
4. **Caméra** : Reconnaissance de texte OCR à partir d'images.

#### Processus de traduction
1. Saisir le texte dans la langue source.
2. Appuyer sur le bouton proéminent "Traduire".
3. L'indicateur de traitement IA montre la progression.
4. Les résultats apparaissent avec l'option de prononciation.
5. Sauvegarde automatique dans l'historique.

#### Fonctionnalités vocales
- **Entrée** : Reconnaissance vocale dans plusieurs langues.
- **Sortie** : Synthèse vocale avec une prononciation correcte.
- **Détection de la langue** : Identification automatique de la langue.

## 🛠 Architecture technique

### Framework Frontend
- **Expo SDK 52.0.30** : Framework de développement multiplateforme.
- **React Native** : Développement d'applications mobiles natives.
- **Expo Router 4.0.17** : Système de routage basé sur les fichiers.
- **TypeScript** : Développement avec typage sécurisé.

### Bibliothèques UI/UX
- **Expo Linear Gradient** : Arrière-plans en dégradé magnifiques.
- **Expo Blur** : Effets de "glass-morphism".
- **Lucide React Native** : Système d'icônes cohérent.
- **Expo Google Fonts** : Famille de polices Inter.

### Fonctionnalités de base
- **Expo Speech** : Fonctionnalité de synthèse vocale.
- **Expo Camera** : OCR et traduction d'images.
- **Expo Haptics** : Retour tactile (mobile uniquement).
- **Better SQLite3** : Stockage de données local.

### Intégration IA
- **Modèle Gemma-3n 4b** : Modèle hors ligne ajusté pour le tamazight (sortie en juin 2025).
- **API Google Gemini** : Mode en ligne utilisant le dernier Gemma-3 12b pour une précision maximale.
- **Traitement double** : IA dans le cloud et sur l'appareil avec basculement automatique.
- **Optimisation pour les urgences** : Inférence priorisée pour la communication critique.
- **TensorFlow Lite** : Déploiement mobile optimisé pour Android et iOS.

### Architecture de base de données double révolutionnaire
- **🌐 Base de données temps réel Convex** : Plateforme collaborative basée sur le cloud.
  - Partage de traductions en temps réel entre utilisateurs du monde entier.
  - Système de diffusion d'urgence avec 10 niveaux de priorité.
  - Vérification par la communauté et précision participative.
  - Préservation culturelle avec documentation du patrimoine berbère.
  - 7 tables avec 30 index optimisés pour la performance.
- **📱 Base de données Expo SQLite** : Stockage local "offline-first".
  - Accès instantané aux phrases d'urgence (fonctionne sans Internet).
  - Traductions pré-mises en cache pour une fiabilité hors ligne.
  - File d'attente de synchronisation automatique pour la restauration de la connectivité.
  - Requêtes locales optimisées pour la batterie (temps de réponse <10ms).

📖 **[Documentation détaillée de l'architecture de la base de données](documentation/DATABASE_ARCHITECTURE.md)**

## 🌍 Système de traduction multidirectionnelle

### **Langues prises en charge**
- **Tamazight (ⵜⴰⵎⴰⵣⵉⵖⵜ)** : Langue berbère autochtone avec prise en charge de l'alphabet tifinagh.
- **Arabe (العربية)** : Arabe standard moderne (co-langue officielle du Maroc).
- **Français** : Langue administrative et éducative.
- **Anglais** : Communication internationale et intervention d'urgence.

### **Matrice de traduction**
**Traduction multidirectionnelle** : L'application facilite la traduction entre toutes les paires de langues :

| De/À | Tamazight | Arabe | Français | Anglais |
|---------|-----------|--------|--------|---------|
| **Tamazight** | — | ✅ | ✅ | ✅ |
| **Arabe** | ✅ | — | ✅ | ✅ |
| **Français** | ✅ | ✅ | — | ✅ |
| **Anglais** | ✅ | ✅ | ✅ | — |

### **Implémentation progressive des langues**
**Version 1.0 (actuelle)** : Focalisation sur le tachelhit et les caractères tifinagh.
**Version 2.0 (prévue)** : Variantes supplémentaires du tamazight (tarifit, tamazight du centre de l'Atlas) après des tests sur le terrain.

Cette approche progressive garantit la qualité et la précision culturelle tout en construisant une base pour une prise en charge complète des langues berbères.

### **Fonctionnalités culturelles et linguistiques**
- **Prise en charge des alphabets** : Systèmes d'écriture latin, arabe et tifinagh (ⵜⵉⴼⵉⵏⴰⵖ).
- **Prise en charge vocale** : Enregistrements audio natifs pour les phrases d'urgence.
- **Contexte culturel** : Phrases adaptées aux contextes sociaux et administratifs marocains.
- **Alignement constitutionnel** : Soutient les droits linguistiques de l'article 5 de la Constitution marocaine de 2011.

## 🚨 Contexte de la communication d'urgence

### **Réponse au tremblement de terre de 2023 au Maroc**
Le tremblement de terre dévastateur qui a frappé le Maroc a mis en évidence des barrières de communication critiques entre les secouristes et les communautés berbérophones. Cette application s'attaque directement à ces lacunes en fournissant :

- **Traduction d'urgence instantanée** : Aucune configuration ou formation requise.
- **Fiabilité hors ligne** : Fonctionne lorsque les réseaux cellulaires sont hors service.
- **Sensibilité culturelle** : Respecte le patrimoine linguistique berbère tout en permettant les interventions d'urgence.
- **Outil pour les secouristes** : Permet une communication efficace avec les populations touchées.

### **Cas d'usage d'urgence**
- **Urgences médicales** : "J'ai besoin d'aide médicale" → "ⵔⵉⵖ ⵜⵉⵡⵉⵙⵉ ⵏ ⵓⵙⴳⵏⴼ"
- **Recherche et sauvetage** : "Où es-tu ?" → "ⵎⴰⵏⵉ ⵜⵍⵍⵉⴷ?"
- **Besoins de base** : "Eau" → "ⴰⵎⴰⵏ", "Nourriture" → "ⵓⵛⵛⵉ"
- **Services de localisation** : "Hôpital" → "ⴰⵙⴳⵏⴼ", "Police" → "ⵍⴱⵓⵍⵉⵙ"

## 📋 Scripts de développement

```bash
# Développement
npm run dev          # Démarrer le serveur de développement
pnpm dev             # Démarrer avec pnpm

# Compilation
npm run build:web    # Compiler pour le déploiement web
pnpm build:web       # Compiler avec pnpm

# Qualité du code
npm run lint         # Exécuter ESLint
pnpm lint            # Linter avec pnpm
```

## 🔧 Configuration

### Variables d'environnement
Créez un fichier `.env` à la racine du projet :

```env
# Configuration de l'API Google Gemini (pour le mode en ligne)
EXPO_PUBLIC_GEMINI_API_KEY=votre-cle-api-gemini-ici

# Configuration de l'application
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_API_URL=https://votre-url-api.com
EXPO_PUBLIC_GEMMA_MODEL_PATH=./models/gemma-3
```

Obtenez votre clé API Gemini gratuite sur : https://ai.google.dev/

### Fonctionnalités spécifiques à la plateforme
L'application détecte automatiquement la plateforme et active/désactive les fonctionnalités :

- **Web** : Fonctionnalité complète sauf les retours haptiques et certaines fonctionnalités natives.
- **iOS/Android** : Ensemble complet de fonctionnalités, y compris les retours haptiques et les API natives.
- **Réactif** : S'adapte aux différentes tailles et orientations d'écran.

## 🚀 Déploiement

### Déploiement Web```bash
npm run build:web
# Déployez le dossier dist sur votre fournisseur d'hébergement
```

### Boutiques d'applications mobiles
1. **App Store iOS** : Utilisez EAS Build pour le déploiement iOS.
2. **Google Play Store** : Utilisez EAS Build pour le déploiement Android.
3. **Expo Updates** : Mises à jour "over-the-air" pour les applications publiées.

### EAS Build (Recommandé)
```bash
# Installer EAS CLI
npm install -g @expo/eas-cli

# Configurer EAS
eas build:configure

# Compiler pour la production
eas build --platform all
```

## 🤝 Contribuer

1. Forker le dépôt.
2. Créer une branche de fonctionnalité (`git checkout -b feature/amazing-feature`).
3. Commiter vos changements (`git commit -m 'Add amazing feature'`).
4. Pousser vers la branche (`git push origin feature/amazing-feature`).
5. Ouvrir une Pull Request.

## 📄 Licence

Ce projet est sous licence MIT - voir le fichier LICENSE pour plus de détails.

### **Partenaires techniques**
- **Google DeepMind** : Pour le modèle Gemma-3n et l'opportunité du hackathon Kaggle.
- **Communauté linguistique tamazight** : Pour les conseils et la validation culturels et linguistiques.
- **IRCAM du Maroc** : Institut Royal de la Culture Amazighe pour les normes de l'alphabet tifinagh.

### **Framework de développement**
- **Équipe Expo** : Pour l'excellent framework de développement multiplateforme.
- **Communauté React Native** : Pour les outils de développement d'applications mobiles.
- **Google Fonts** : Pour la magnifique famille de polices Inter.
- **Lucide Icons** : Pour la bibliothèque d'icônes complète.