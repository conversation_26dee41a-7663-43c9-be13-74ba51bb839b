

# **Production Deployment and Configuration Guide for the Tamazight MultiLingo Application**

## **Section 1: Production Readiness Analysis of the Tamazight MultiLingo App Codebase**

A thorough analysis of the provided application codebase is the foundational step for architecting a successful and maintainable production deployment. This examination reveals critical configuration details within the project's structure that directly inform the deployment strategy for both the Convex backend and the Netlify frontend.

### **1.1 Deconstructing the package.json Build Scripts**

The package.json file serves as the manifest for the project, defining its dependencies and, most importantly, its operational scripts. The scripts object contains several commands, but one is paramount for the web deployment process.1

* **"build:web": "expo export \--platform web"**: This is the designated command for creating a production-ready, static build of the Expo web application. The expo export command is specifically designed to compile and bundle all necessary assets—JavaScript, CSS, images, and fonts—into a self-contained directory that can be served by a static hosting provider like Netlify. This script is the core of the frontend build stage in the deployment pipeline.

Other scripts, such as "dataset:import" and "dataset:clear", indicate a dependency on a local SQLite database that is seeded with external data for the application's offline functionality.1 While these scripts are not executed during the online deployment process, their presence highlights the application's sophisticated dual-mode architecture. This reinforces the necessity of establishing a robust, independent production backend on Convex to complement and synchronize with the app's offline capabilities.

### **1.2 Interpreting the app.json Web Configuration**

The app.json file contains essential configuration for the Expo framework, including platform-specific settings. The web configuration block dictates how the web version of the application is built and behaves.1

* **"output": "single"**: This configuration instructs Expo to build the application as a Single-Page Application (SPA). In an SPA architecture, a single index.html file is generated, and all subsequent navigation and view changes are managed client-side by a JavaScript router—in this case, Expo Router.

This seemingly simple setting has profound implications for deployment on a static hosting platform. When a user attempts to navigate directly to a specific page within the app (for example, by entering https://your-app.netlify.app/history into their browser), the browser sends a request for the /history resource to Netlify's servers. Because the site is static and was built with the "single" output option, no history.html file exists in the deployment directory. Consequently, Netlify's default behavior is to return a 404 "Page Not Found" error, leading to a broken user experience.

To resolve this, a server-side rewrite rule must be configured. This rule instructs the hosting provider to serve the root index.html file for any incoming request that does not correspond to an existing static asset (like a CSS file or an image). Once index.html is loaded, the client-side Expo Router initializes, reads the URL path (/history), and correctly renders the corresponding screen. This configuration is a critical, non-obvious requirement for the successful deployment of any SPA and will be addressed explicitly in the deployment workflow.2

### **1.3 Identifying Critical Environment Variables**

The .env.example file provides a template for the environment variables required to run the application. These variables are the crucial link between the frontend application and its backend services.1

* **EXPO\_PUBLIC\_CONVEX\_URL**: This variable holds the public-facing URL of the Convex backend. The client-side application uses this URL to establish its real-time WebSocket connection to the database. During the build process, this variable must be populated with the URL of the **production** Convex deployment.  
* **CONVEX\_DEPLOYMENT**: This variable, as listed in the example, is a placeholder for a deployment key. Official Convex documentation clarifies that for automated CI/CD environments like Netlify, the specific environment variable that must be set is CONVEX\_DEPLOY\_KEY.3 This key is a secret that authorizes the Netlify build server to execute deployment commands against the Convex project on the developer's behalf.

## **Section 2: Establishing the Convex Production Backend**

The user query indicates an existing development setup on Convex but no production environment. Creating a distinct and isolated production backend is the most critical step in moving the application from development to a live, user-facing state. This separation is a fundamental principle of modern application lifecycle management, ensuring data integrity, security, and stability.

### **2.1 The Imperative of Environment Isolation: Development vs. Production**

Convex's architecture provides separate deployments for development and production.4 The existing instance,

development-canny-dalmatian-335, was created by running npx convex dev and is intended for local development. It allows for rapid iteration, including frequent and potentially destructive schema changes, without impacting live users.

A production deployment, by contrast, is the single, canonical backend that serves the live application. It contains real user data and must be treated with stringent access controls and a disciplined update process. Deploying a live application against a development instance would expose it to catastrophic risks, such as a developer accidentally wiping the production database while testing a migration script.

Therefore, the correct procedure is not to "promote" the development instance but to create an entirely new, parallel production environment associated with the same project. This production environment will have its own isolated database and will be managed via a unique, highly sensitive Production Deploy Key.

### **2.2 Step-by-Step Guide to Creating a Production Deployment**

The creation of a production deployment and its associated deploy key is performed through the Convex Dashboard. The following steps provide an explicit, sequential guide to this process.

1. Navigate to the Convex Dashboard by opening https://dashboard.convex.dev in a web browser.  
2. Log in and select the correct project from the project list: **canny-dalmatian-335**.  
3. Once on the project dashboard, navigate to the **Settings** tab in the main navigation menu.  
4. On the Settings page, locate the section titled **URL and Deploy Key**.6 Within this section, find the subsection for the  
   **Production** deployment.  
5. Click the **Generate** button located within the "Production Deploy Key" area.3 A new, unique alphanumeric key will be created and displayed.  
6. Immediately click the **Copy** button next to the generated key. This action securely copies the key to the clipboard. This key is a sensitive credential and must be handled securely; it should not be committed to version control.

## **Section 3: Configuring Netlify for Continuous and Integrated Deployment**

With the production backend ready, the next phase is to configure the Netlify project to build the Expo web application and integrate it with the newly established Convex environment. This is achieved through a continuous deployment pipeline linked to a Git repository.

### **3.1 Initializing the Netlify Project from a Git Repository**

The standard and recommended workflow for deploying applications on Netlify is through a Git-based process.3 This approach enables continuous deployment, where any push to the main branch of the repository automatically triggers a new build and deployment on Netlify.

The process begins in the Netlify UI by selecting **"Import from Git"** (or "Add new site"). After authorizing with the appropriate Git provider (e.g., GitHub), the tamazight-multilingo-app repository should be selected. This will lead to the build configuration screen, where the deployment parameters must be set with precision.

### **3.2 The Definitive Build Configuration**

The following table provides the single source of truth for the Netlify build configuration. Each setting is derived from a synthesis of the codebase analysis and official documentation from both Convex and Expo.

| Setting | Value | Rationale and In-Depth Explanation |  |  |
| :---- | :---- | :---- | :---- | :---- |
| **Build command** | $npx convex deploy \--cmd 'npm run build:web' | **Analysis:** This command orchestrates a precise, two-stage deployment, which is the most critical configuration detail.3 | **Stage 1 (npx convex deploy):** This command executes first. It reads the CONVEX\_DEPLOY\_KEY from the Netlify environment, authenticates with the Convex project, and pushes all functions and schema definitions from the convex/ directory to the **production** deployment. **Stage 2 (--cmd 'npm run build:web'):** Only after the backend deployment completes successfully, this command executes npm run build:web. This script, defined in package.json, resolves to expo export \--platform web.1 The Expo build process is designed to automatically detect the production | CONVEX\_URL (which is set by the convex deploy command) and embed it directly into the static frontend files. **Causal Relationship:** This specific sequence is non-negotiable. It guarantees that the statically generated frontend is correctly configured to communicate with the newly deployed production backend, preventing fatal connection errors in the live application. |
| **Publish directory** | dist | **Analysis:** The expo export \--platform web command compiles the web application and places all resulting static assets (HTML, CSS, JavaScript) into a directory named dist.2 This is the directory that Netlify must serve to the public as the root of the website. |  |  |
| **Environment Variable: Key** | CONVEX\_DEPLOY\_KEY | **Analysis:** This is the exact, case-sensitive key name required by the npx convex deploy command to authenticate with the Convex backend during the build process.3 This variable must be configured within the Netlify UI under "Site configuration" \> "Environment variables". |  |  |
| **Environment Variable: Value** | \`\` | **Analysis:** The value for this key is the unique string generated in Section 2.2. This key grants the Netlify build server the necessary permissions to deploy code changes to the Convex production backend on the project's behalf. |  |  |

## **Section 4: The Definitive LLM-Ready Deployment Workflow**

This section synthesizes all prior analysis into a single, chronological, and unambiguous set of instructions designed for flawless execution.

### **4.1 Pre-flight Check**

1. Confirm that the complete v3.6-tamazight\_multilingo\_app project code has been pushed to a GitHub repository.  
2. Confirm access to a Netlify account and the Convex account associated with the project.  
3. Confirm the Convex project name is **canny-dalmatian-335** and the development deployment is **tough-antelope-751**.

### **4.2 Phase 1: Configure the Production Backend**

1. Execute the six steps detailed in **Section 2.2** to generate and copy the **Production Deploy Key** from the Convex Dashboard.  
2. Store this key securely in a temporary location, such as a password manager or a secure note.

### **4.3 Phase 2: Prepare the Codebase for SPA Deployment**

1. In the root directory of the local project (mindful-ai-dude-v3.6-tamazight\_multilingo\_app/), create a new file.  
2. Name this file exactly \_redirects (with no file extension).  
3. Add the following line of text as the sole content of this file: /\* /index.html 200\.  
4. This step is critical for ensuring the Single-Page Application's routing functions correctly on Netlify, as identified in Section 1.2.2  
5. Save the \_redirects file.  
6. Commit and push this new file to the GitHub repository.

### **4.4 Phase 3: Configure and Trigger Netlify Deployment**

1. Navigate to https://app.netlify.com/start in a web browser.  
2. Follow the UI prompts to **"Import from Git"** and connect the GitHub repository containing the application code.  
3. When presented with the **"Build settings"** page, input the exact values from the configuration table in **Section 3.2**:  
   * **Build command:** $npx convex deploy \--cmd 'npm run build:web'  
   * **Publish directory:** dist  
4. Before initiating the deploy, navigate to the site's dashboard, then go to **"Site configuration" \> "Environment variables"**.  
5. Click **"Add environment variable"**.  
6. Set the **Key** field to CONVEX\_DEPLOY\_KEY.  
7. Paste the **Production Deploy Key** obtained in Phase 1 into the **Value** field.  
8. Click **"Create variable"** to save it.  
9. Navigate to the **"Deploys"** tab for the site and click the **"Trigger deploy"** dropdown, then select **"Deploy site"**. The deployment process will now begin.

## **Section 5: Post-Deployment Verification and Operational Best Practices**

A successful deployment is not merely a "green" build status; it is a fully verified, functional application. This final section provides a protocol for ensuring the application is truly production-ready and outlines a strategy for future maintenance and updates.

### **5.1 Verifying a Successful Deployment**

A multi-faceted approach is required to confirm that all components of the deployment are functioning correctly.

1. **Netlify Verification:** In the Netlify UI, inspect the deploy log for the most recent build. Confirm that the log shows a "Site is live" message. Critically, scroll through the log to verify that the $npx convex deploy step completed successfully *before* the $npm run build:web step was initiated.  
2. **Application Verification:** Open the live Netlify URL (e.g., https://\<site-name\>.netlify.app). Open the browser's developer tools and inspect the "Network" tab, filtering for "WS" (WebSocket) connections. Verify that a WebSocket connection is successfully established with a production Convex URL (e.g., wss://\<some-name\>.convex.cloud), and not a local or development URL. Test the core functionality, such as performing a translation, to ensure it interacts with the backend as expected.  
3. **Convex Verification:** Navigate back to the Convex Dashboard for the project. At the top-left of the page, use the deployment dropdown menu to switch from your **dev** deployment (e.g., tough-antelope-751) to the **prod** deployment. Navigate to the "Functions" and "Logs" tabs. Confirm that the latest versions of the backend functions have been deployed and that logs are appearing from interactions with the live Netlify site without any errors.

### **5.2 Managing Future Updates and Schema Migrations**

Production environments require a disciplined and safe update process, especially when database schema changes are involved. The separation of development and production environments enables a robust, Git-centric workflow.

Convex enforces a critical constraint: a new schema pushed to a deployment must always be compatible with the data that already exists in that deployment's database.5 This prevents data corruption but requires a careful migration strategy. The recommended best practice workflow is as follows:

1. **Develop Locally:** All new features, schema changes, and function modifications should be developed on a local machine. The developer runs npx convex dev to connect to their personal dev deployment, allowing for safe experimentation.  
2. **Commit and Push:** Once development is complete, changes are committed and pushed to a feature branch in the GitHub repository.  
3. **Deploy Preview (Advanced):** For critical changes, especially those involving schema migrations, Netlify's Deploy Previews can be integrated with Convex Preview Deployments.3 This creates a temporary, isolated staging environment for each pull request, allowing for thorough testing before merging.  
4. **Merge to Main:** After the pull request is reviewed and approved, it is merged into the main branch.  
5. **Automatic Production Deploy:** This merge automatically triggers the Netlify deployment pipeline. The npx convex deploy \--cmd 'npm run build:web' command runs again, safely updating the production Convex backend with the new functions and schema, followed by the deployment of the new frontend.

This workflow provides a safe, repeatable, and automated process for managing the entire application lifecycle, ensuring the production environment remains stable and secure.

#### **Works cited**

1. v3.6-tamazight\_multilingo\_app-full-codebase.txt  
2. Publish websites \- Expo Documentation, accessed August 6, 2025, [https://docs.expo.dev/guides/publishing-websites/](https://docs.expo.dev/guides/publishing-websites/)  
3. Using Convex with Netlify | Convex Developer Hub, accessed August 6, 2025, [https://docs.convex.dev/production/hosting/netlify](https://docs.convex.dev/production/hosting/netlify)  
4. Deployments | Convex Developer Hub, accessed August 6, 2025, [https://docs.convex.dev/dashboard/deployments/](https://docs.convex.dev/dashboard/deployments/)  
5. Deploying Your App to Production | Convex Developer Hub, accessed August 6, 2025, [https://docs.convex.dev/production](https://docs.convex.dev/production)  
6. Settings | Convex Developer Hub \- Convex Docs, accessed August 6, 2025, [https://docs.convex.dev/dashboard/deployments/deployment-settings](https://docs.convex.dev/dashboard/deployments/deployment-settings)  
7. Using Convex with Vercel | Convex Developer Hub, accessed August 6, 2025, [https://docs.convex.dev/production/hosting/vercel](https://docs.convex.dev/production/hosting/vercel)  
8. Start from anywhere | Netlify Docs, accessed August 6, 2025, [https://docs.netlify.com/start/overview/](https://docs.netlify.com/start/overview/)  
9. Hosting and Deployment | Convex Developer Hub, accessed August 6, 2025, [https://docs.convex.dev/production/hosting/](https://docs.convex.dev/production/hosting/)  
10. I want to deploy my website that uses convex development environment, how do I deal with SITE\_URL? \- Answer Overflow, accessed August 6, 2025, [https://www.answeroverflow.com/m/1272605428735283251](https://www.answeroverflow.com/m/1272605428735283251)