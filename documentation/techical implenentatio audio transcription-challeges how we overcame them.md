### Audio File 1

**00:00-01:04**
Let's see. Oh, now it's recording. Okay. So, so one of the features of the application, um, yeah, because of the challenges that we had and that are in the, um, first audio recording, because of the challenges that we had and every developer had with actually implementing the on-device aspect of the 3N models, because that's their intended purpose to be able to be used on, um, mobile devices or on laptops or on tablets. Um, but because of the of not being able to overcome the, um, lack of libraries and frameworks, um, and code from Google. Um, that part was not, we were not able to implement that. So, um, how we overcame that challenge is, um, we took, um, part of the data sets, the data set pairs, um, that we used in the fine tuning jobs.

**01:04-02:07**
Um, we used our part of our eval data set and we took, um, which, yeah, which, uh, can, I'll, I'll come back to that. Um, so we took part of that eval data set and we inserted part of it into the local SQLite, S-XPO, EXPO, SQLite database that's running locally on my laptop, but SQLite, the Expo XQLite database can run on any, um, on the offline on mobile phones, on laptops, on tablets. So that's the way we overcame the challenge of, um, being able to, uh, actually utilize the app and demo the app. And we also created another feature, um, to, um, so the app is designed to be, um, utilized by rescue workers, by government officials, by anyone, um, that is trying to help the, um, native, um, Morocco Berber community, which is quite substantial, um, numbering over a million individuals.

**02:07-03:10**
In Morocco, there are, um, three Tamazight, uh, dialects. And we have utilized, um, one of them, um, for our app. Uh, and that's Tachelhit, which is spelled T A C H E L H I T, I believe. Um, but you can check that, um, using your grounded research. Um, yeah, so we wanted to get one of the dialects, um, uh, to create data sets, which we did, and we fine-tuned numerous models that are on Hugging Face, uh, with our intention to, um, add the other two dialects of Tamazight later.

**03:10-04:14**
And our app, uh, has the ability to be offline, um, to be used in crisis situations. But we also implemented another feature, um, where you can toggle on a button and if you have internet connectivity, um, it can be used online. And part of that online functionality is to utilize, instead of the SQLite database, um, we have configured, uh, Convex database, um, which, yeah, we'll leave, uh, yeah, I have screenshots, um, to that functionality or screen recordings. Um, and yeah, it's really, uh, quite innovative and, um, quite amazing as you will be able to attest from the screenshots. So, we, yeah, it's offline first and then online as a second, um, additional feature because the larger Gemma 3 models, um, the 12 billion parameter and the 27 billion parameter are, um, extremely performant.

**04:14-05:18**
Higher parameters, the more data that they have been trained on. Um, one of the really great things about all of the Gemma models, open source models, is that because, uh, Google, I believe in 2003, added, um, Tamazight and Tifinagh characters to their, um, extensive, uh, translation, language translation library. I think it's 140 plus languages now that Google has. Um, and that's what really makes Google, um, stand out when it comes to translation. Um, and the fact that they've added indigenous languages is really amazing. So, um, yeah, so that's the way we overcame the challenge of not being able to put the device, um, the Gemma 3N model on device is, um, to, uh, upload data sets.

**05:18-06:21**
Part of our, in the settings, you can also upload a data set, um, that can be stored on device. There's a button in the settings. You can also, um, yeah, get a visualization of that, um, which you, yeah, which we also demonstrate. And, um, so those are also part of the technical choices that must go in the technical paper that, um, that we made. Um, let me think of what other technical choices that are, um, appropriate. I think that's actually it as far as the technical choices that we made. And also, you can see very clearly by the attachments, the technical choices that we made and, um, and it's up to you, the AI and LLM, to, um, to answer the questions based on the documents that I am providing.

**06:21-06:57**
Um, to answer the question, why our technical choices were the right ones. And, um, also part of the technical paper and why this is important to the judging committee is because they need to be able to validate the authenticity, um, of our project.

### Audio File 2

**00:00-00:30**
Testing, testing, 1, 2, 3, testing, testing. So, this is an audio recording for the technical write-up paper for the Kaggle Google DeepMind Gemma, that's spelled G E M M A, 3N Hackathon challenge.

**00:30-01:00**
And I would like Google AI Studio to transcribe this audio message. So, the technical paper is the technical verification that clearly must explain the architecture of our app. I have attached several documents, um, including the full code base and some other architecture, um, mermaid diagrams as well as some HTML visualization files.

**01:00-01:30**
So, the first question is how we specifically used Gemma 3N, the challenges that we overcame, and why our technical choices were the right ones. So, to answer the first question, um, which is a little bit, um, nuanced, is we, in the end, there is really no developer who could integrate Gemma 3N on device as was and as is stated, um, in the app submission documentation.

**01:30-02:00**
Um, why that is, and in fact, I am including the links to the articles because or to the links to the reasons why, including some images from the Unsloth team, is that, um, the Gemma 3N model was released in June of 2025. Today's current date is August 6th, 2025. And and the TFLite libraries and frameworks and the LiteRT, which is still in development frameworks, um, there is no, um, implementation documentation for the Gemma 3N model.

**02:00-02:30**
And that is due to the fact that not only is the Gemma 3N model new, but its architecture is completely different than any of the other Gemma 3 models because it uses, um, something similar to a mixture of experts model. Um, and Google calls it, and I will attach the link to what it's called, but it's similar to a Matryoshka Russian doll model.

**02:30-03:00**
So, it's multi-layered. It's really, um, quite unique and innovative. But because it's so new and the architecture is vastly different than the other Gemma 3 non-N models, there is not up until this, up until today, August 6th, any, um, documentation on GitHub or any place else, because I used Google Deep Research to do, um, an extensive, yeah, extensive research.

**03:00-03:30**
And its findings were that even the top ML experts have not come up with, uh, a way to get the Gemma, the Gemma 3N model, um, on device. And that's one of the huge benefits of this Gemma 3N, yeah, there's two different, um, open source models. One is a 2 billion parameter model and the other is a 4 billion parameter model.

**03:30-04:00**
And so, there was actually no way to specifically use Gemma 3N on device, um, or to use its multimodal capabilities, which is also in the write-up. Um, even within Google AI Studio, uh, when you go to upload an image, um, there is no way to record an audio in AI Studio.

**04:00-04:30**
Um, but when you go to upload an image, um, it clearly shows that images are not, yeah, that images can't be accepted, um, as of this date. And I have screen captured the output from AI Studio to, um, prove this fact. Um, additionally, Hugging Face has its ONNX models, which you can use to put on device. Um, but, um, it also is not, there is also no implementation for Gemma 3N.

**04:30-05:00**
One of the, so these are, um, the reasons why, um, we couldn't specifically use Gemma 3N and also goes into the challenges. Um, and at the end of this, um, this particular part of my recording that is directly related to the challenges we overcame, but I'm beginning with the challenges period. Um, yeah, so Hugging Face has no, the ONNX framework does not work.

**05:00-05:30**
Um, the, um, I, I personally spent, um, weeks online with the Unsloth team. And the Unsloth team, and I will include the link also, worked with directly with the Google DeepMind Gemma 3N team to produce their fine-tuning notebook.

**05:30-06:00**
And within the fine-tuning, Unsloth's not not fine-tuning notebook, which actually fine-tunes the model, um, uh, perfectly. And I have fine-tuned several versions, um, and it output the merged B, um, F, uh, I think it's F float 16 or B float 16, um, merged model, merged fine-tuned models as well as, uh, the LORAs.

**06:00-06:30**
So those two things, um, actually worked well. Um, at the bottom of the Unsloth, um, notebook is, um, the export to GGUF format, which you can use, generally use in a Llama. And, um, and, um, which are quantized versions of, of the Gemma 3 LLM or AI.

**06:30-07:00**
Um, but also, yeah, quantization is something that you can do for every other model. Um, because also, as a result of the Gemma 3N architecture, the Llama C++ team that actually is responsible for the conversion to GGUF quantized formats does not work with the Gemma 3N model. Um, and that, uh, is something that you can clearly see in the screenshots from the, um, um, from the Unsloth, uh, Discord communication from the Unsloth developers.

**07:00-07:30**
So these were all, um, really big challenges. Um, no TFLite integration capability, no LiteRT, um, ability to do the conversion, even the media, media pipe inference, um, for on device, um, that doesn't work either for the Gemma 3N model. So those, um, were the challenges that we faced and, you know, and, um, numerous other devs faced, um, as noted by the conversations that I've had for the last few weeks with the Unsloth team and with other developers.

**07:30-08:00**
Throwing no shade on Google, it's a brand new model and, um, and most companies release their brand new models to developers for testing, for finding bugs. Um, and because this model is so, its architecture is so innovative, um, yeah, the, it, it just really was not possible to get the, um, to get the 3N models to place them on device.

**08:00-08:30**
I tried, um, numerous times and I was actually, um, what I thought was successful in, um, fine-tuning or converting my the merged B float 16, um, fine-tuned jobs to GGUF. Um, and it looked like, and as you will be able to see by the links from my Hugging Face page, that the conversion process actually worked.

**08:30-09:00**
Um, but then when I tried to, uh, run the models in, um, a Llama, um, they completely failed. And, um, I also have those screen captures. And I was in contact with the Llama C++ team and, yeah, um, as of yet, there is no workaround. There are several PRs on the Llama C++ website or GitHub, um, repository.

**09:00-09:30**
And, um, also on Google's repository, there are also several PRs and I placed, um, the links to the Google, um, PRs below. Um, why our, so the technical choices that we ended up making is we, in fact, fine-tuned several, I mean, probably about 15, um, uh, Gemma 3N 2 billion parameter and 4 billion parameter models, as you can see, you'll be able to see in my Hugging Face page.

**09:30-10:00**
Um, um, B float 16 and also LORAs. And in the end, what, um, I ended up doing is, uh, because our app is a multilingual app and it's meant to be a multilingual, or it is a multilingual app that translates, um, uh, between, uh, the Moroccan Berber indigenous language of Tamazight, which is spelled T A M A Z I G H T, but pronounced Tamazight, like T I R T.

**10:00-10:30**
Um, which is, uh, supported language, um, on Google's, uh, yeah, Google, I think as of, uh, June 2023 or 24, started supporting, um, Tamazight, um, and its Tifinagh, uh, script. Um, and that's one of the reasons why I was really attracted to this because the application is meant, is designed to serve a real world in real world crisis and emergency situations as a result of the 2023 earthquake that happened in Morocco.

**10:30-11:00**
So, the way that we overcame the challenge, um, in order to submit our app, um, in the hackathon is we loaded data sets, um, part of our data sets, um, part of the evaluation data set, um, because we created a test data set that has more than 100,000 samples and then a, um, evaluation data set. And so, um, I took part of the evaluation data set and, um, have, uh, injected that into the, um, on-device, um, Expo SQLite, um, database.

