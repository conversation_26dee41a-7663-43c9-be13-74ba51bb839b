{"version": "2.0.0", "tasks": [{"label": "Clear TypeScript Cache", "type": "shell", "command": "rm", "args": ["-rf", ".expo", "node_modules/.cache", ".metro-cache"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Restart TypeScript Server", "type": "shell", "command": "echo", "args": ["TypeScript server restart requested"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}