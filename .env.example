# Google Gemini API Configuration
# Get your free API key from: https://ai.google.dev/
EXPO_PUBLIC_GEMINI_API_KEY=your-gemini-api-key-here

# App Configuration
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_API_URL=https://your-api-url.com

# TFLite does not work at all
# Workaround is to use gguf and llama.rn
EXPO_PUBLIC_GEMMA_MODEL_PATH=./models/gemma-3n

# Convex Database Configuration for online feature
# Real-time database for collaborative translations and emergency communication
EXPO_PUBLIC_CONVEX_URL=http://127.0.0.1:3210

# Deployment used by `npx convex dev`
CONVEX_DEPLOYMENT='api key goes here'



