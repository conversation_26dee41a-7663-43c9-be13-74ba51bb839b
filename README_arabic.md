# تطبيق Multi-Lingo - إصدار تمازيغت (تمازيرت)
![عرض توضيحي للتطبيق](assets/images/image-1-tamazight-eyes-intro1280x720px-no-bg.gif)

الكود المصدري المفتوح لتطبيق Tamazight Multi-Lingo على Github:
https://github.com/mindful-ai-dude/v3.6-Tamazight_MultiLingo_App

بطاقات النماذج ومجموعات البيانات لتطبيق Tamazight Multi-Lingo على Hugging Face:
https://huggingface.co/tamazightdev

## جسر بين اللغات، حفاظ على الثقافة، وتمكين التواصل في حالات الطوارئ.

*"في الأزمات، كل كلمة تهم. في الحفاظ على التراث، كل لغة تُحتسب."*

### 🌍 المهمة: سد فجوات التواصل في الأزمات

يعالج هذا التطبيق حاجة إنسانية ماسة تم تحديدها خلال زلزال المغرب المدمر عام 2023، حيث واجه عمال الإنقاذ والمسؤولون صعوبة في التواصل الفعال مع المجتمعات الأمازيغية المغربية. يقدم حلنا ترجمة فورية تعمل بالذكاء الاصطناعي دون اتصال بالإنترنت لضمان عدم ترك أي شخص خلف الركب أثناء حالات الطوارئ.

تطبيق ترجمة محمول جميل وجاهز للإنتاج، تم بناؤه باستخدام Expo و React Native، ويتميز بقدرات ترجمة بالذكاء الاصطناعي على الجهاز دون اتصال بالإنترنت، وعبر الإنترنت للغة تمازيغت (ⵜⴰⵎⴰⵣⵉⵖⵜ)، التي تُنطق "تمازيرت"، وهي اللغة الأمازيغية الأصلية في المغرب. يوفر التطبيق ترجمة سلسة متعددة الاتجاهات بين تمازيغت والعربية والفرنسية والإنجليزية مع ميزات متخصصة لحالات الطوارئ، والاستخدام الحكومي/البرلماني، وكأداة ترجمة عامة متعددة اللغات.

## عني، غريغوري كينيدي، مبتكر ومطور التطبيق:
- أنا مهندس سابق في وادي السيليكون ومخرج أفلام حائز على جوائز، حظيت بشرف وسرور السفر حول العالم والعمل مع فائزين بجائزة نوبل للسلام (أدولفو بيريز إسكيفيل وميريد كوريجان ماغواير)، وخبيرة الشمبانزي الدكتورة جين غودال في أفريقيا وأوروبا، ومع معلم الزن ثيت نات هانه في فرنسا وألمانيا والولايات المتحدة الأمريكية. لقد دعيت وتحدثت أمام جماهير في الأمم المتحدة في فيينا، النمسا، ونيويورك، وفي جامعة ستانفورد.

- أنا مواطن أمريكي تعيش عائلتي في الولايات المتحدة منذ أكثر من 400 عام، ويعكس أصلي تاريخ أمريكا المكون من قبيلتين من الأمم الأولى الأمريكية (إيتاكابا وبلاكفوت)، بالإضافة إلى أصول أفريقية وأيرلندية وفرنسية ومكسيكية.

LinkedIn: لمزيد من المعلومات عني، يرجى زيارة ملفي الشخصي على LinkedIn:
https://www.linkedin.com/in/gregorykennedymindfuldude/

IMDB: فيلم The 5 Powers الحائز على جوائز:
https://www.imdb.com/title/tt2771174/?ref_=mv_desc

## 🙏 شكر وتقدير

- **أمي الرائعة فاليري التي توفيت قبل بضعة أشهر**: التي علمتني الاهتمام بالآخرين، واستكشاف العالم، ومعاملة الجميع باحترام وكرامة.

![غريغوري كينيدي ووالدته فاليري في بن آند جيريز سان فرانسيسكو هايت آشبوري أبريل 2016](assets/images/Gregory%20Kennedy%20and%20mom%20Valerie%20at%20Ben%20and%20Jerry's%20San%20Francisco%20Haight%20Ashbury%20April%202016.jpg)
غريغوري ووالدته في بن آند جيريز سان فرانسيسكو:

- **زملائي المغاربة**: عمر (أمازيغي مغربي)، حمزة، وزياد لتفانيهم وعملهم الجاد. شكراً لكم وأقدركم!

- **ضحايا زلزال المغرب 2023**: هذا التطبيق يكرم المتضررين ويهدف إلى منع حواجز التواصل في المستقبل.

- **المجتمعات الأمازيغية المغربية**: للحفاظ على اللغة والثقافة الأمازيغية عبر القرون.

- **المملكة المغربية**: لإصدار قوانين للحفاظ على اللغة والثقافة الأمازيغية وتكريمها، ولجعل تمازيغت لغة رسمية للمغرب، ولجعل تمازيغت إلزامية في المدارس.

- **المستجيبون للطوارئ**: الذين ألهموا هذا الحل من خلال تفانيهم أثناء الأزمة.

- **Kaggle و Google DeepMind**: لنموذج Gemma-3n وفرصة الهاكاثون من Kaggle.

- **فريق Unsloth (Discord) وأصدقاء التطوير**: لدعمهم وتوجيههم المذهل.

## 🏆 هاكاثون Kaggle Google Deep Mind

### **تفاصيل المسابقة**
- **الحدث**: [هاكاثون Kaggle Google Deep Mind](https://www.kaggle.com/competitions/google-gemma-3n-hackathon)
- **الجدول الزمني**: يوليو 2025 - 6 أغسطس 2025
- **النموذج**: نموذج Gemma-3n بحجم 2 مليار معلمة (صدر في يونيو 2025)
- **التركيز**: تطبيقات الذكاء الاصطناعي الإنسانية باستخدام أحدث نموذج من Google وهو Gemma-3n بحجم 2 مليار معلمة (للاستخدام دون اتصال) وواجهة برمجة تطبيقات Gemma-3 12b (للاستخدام عبر الإنترنت) للتطبيقات الإنسانية لنماذج اللغة الكبيرة.

### **الإنجازات التقنية**
- **مجموعة بيانات الضبط الدقيق**: أكثر من 100,000 زوج لغوي بين العربية والإنجليزية والفرنسية وتيفيناغ.
- **بنية مزدوجة**: واجهة برمجة تطبيقات عبر الإنترنت + استدلال على الجهاز دون اتصال.
- **تحسين للطوارئ**: تدريب متخصص لسيناريوهات التواصل في الأزمات.
- **الدقة الثقافية**: دمج الخبرة اللغوية الأمازيغية المغربية.

![لقطة شاشة للتطبيق 2](assets/images/image-3-v3.6-Tamazight_MultiLingo_App-main-web.png)

### **أبرز الابتكارات**
- **أول ذكاء اصطناعي للغة تمازيغت**: ريادة في ترجمة الذكاء الاصطناعي للغات الأمازيغية الأصلية.
- **تصميم يركز على الطوارئ أولاً**: يعطي الأولوية لحالات الاستخدام الإنساني على الترجمة العامة.
- **الامتثال الدستوري**: يدعم الاعتراف باللغة الرسمية في المغرب.
- **موثوقية دون اتصال**: ضروري لسيناريوهات الكوارث مع البنية التحتية المتضررة.
- **الحفاظ على الثقافة**: يعزز لغة مهددة بالانقراض من خلال التكنولوجيا الحديثة.

### **مقاييس التأثير**
- **المستخدمون المستهدفون**: المستجيبون للطوارئ، المجتمعات الأمازيغية، المسؤولون الحكوميون.
- **حالات الاستخدام**: الإغاثة من الزلازل، الطوارئ الطبية، الإجراءات القانونية.
- **اللغات المحفوظة**: متغيرات تمازيغت (تاشلحيت، تاريفيت، تمازيغت الأطلس المتوسط).
- **عبارات الطوارئ**: أكثر من 50 عبارة تواصل حرجة محملة مسبقًا.

## 🌟 الميزات

### 🚨 تصميم يركز على الطوارئ أولاً
- **التواصل في الأزمات**: مصمم خصيصًا لسيناريوهات الزلازل والإغاثة في حالات الكوارث.
- **يعمل دون اتصال أولاً**: يعمل بدون اتصال بالإنترنت عندما تكون البنية التحتية متضررة.
- **عبارات الطوارئ**: عبارات حرجة محملة مسبقًا للحالات الطبية والإنقاذ.
- **وصول فوري**: لا يتطلب إعدادًا - يعمل على الفور للمستجيبين للطوارئ.

### 🤖 ترجمة متقدمة بالذكاء الاصطناعي
- **ترجمة بوضعين**:
  - **عبر الإنترنت**: أحدث واجهة برمجة تطبيقات Gemma-3 12b لأعلى دقة.
  - **دون اتصال**: نموذج Gemma-3n 2b مضبوط بدقة على الجهاز للخصوصية والموثوقية.
- **ترجمة متعددة الاتجاهات**: العربية ↔ تمازيغت، الفرنسية ↔ تمازيغت، الإنجليزية ↔ تمازيغت.
- **مراعاة السياق**: سياقات الطوارئ والحكومة والعامة مع مصطلحات متخصصة.
- **الدقة الثقافية**: مضبوط بدقة على أكثر من 100,000 زوج لغوي مغربي.

### 🔤 دعم لغة تمازيغت
- **خط تيفيناغ**: دعم أصلي لنظام الكتابة ⵜⵉⴼⵉⵏⴰⵖ.
- **التركيز على تاشلحيت**: الإصدار الأولي محسن لمتغير تمازيغت تاشلحيت.
- **الاعتراف الدستوري**: يتماشى مع المادة 5 من دستور المغرب لعام 2011.
- **الحفاظ على الثقافة**: يعزز ويحافظ على التراث الأمازيغي الأصلي.

### 📱 تجربة المستخدم
- **واجهة مستخدم زجاجية جميلة**: واجهة حديثة وأنيقة مع خلفيات متدرجة.
- **تكامل صوتي**: إدخال الكلام إلى نص وإخراج النص إلى كلام مع صوت أصلي.
- **سجل الترجمة**: قاعدة بيانات SQLite على الجهاز دون اتصال (قاعدة بيانات Convex في الوقت الفعلي عبر الإنترنت) مع المفضلة ووظيفة البحث.
- **تصميم متجاوب**: يناسب تمامًا جميع أحجام الشاشات مع معالجة مناسبة للمنطقة الآمنة.
- **ردود فعل لمسية**: استجابات لمسية لتفاعل أفضل مع المستخدم (للجوال فقط).

## 🚀 التثبيت

### المتطلبات الأساسية
- Node.js 18+
- مدير الحزم npm أو pnpm
- Expo CLI (اختياري، لميزات تطوير إضافية)

### باستخدام npm

```bash
# استنساخ المستودع
git clone <repository-url>
cd tamazight-translate

# تثبيت التبعيات
npm install

# بدء خادم التطوير
npm run dev
```

### باستخدام pnpm

```bash
# استنساخ المستودع
git clone <repository-url>
cd tamazight-translate

# تثبيت التبعيات
pnpm install

# بدء خادم التطوير
pnpm dev```

### خادم التطوير

بعد تشغيل أمر التطوير، سترى:

```
Starting project at /home/<USER>
› Metro waiting on exp://192.168.1.100:8081
› Scan the QR code above with Expo Go (Android) or the Camera app (iOS)
› Press a │ open Android
› Press i │ open iOS simulator
› Press w │ open web
```

- **الويب**: اضغط على `w` للفتح في متصفحك.
- **الجوال**: امسح رمز الاستجابة السريعة باستخدام تطبيق Expo Go.
- **محاكي iOS**: اضغط على `i` (يتطلب Xcode).
- **محاكي Android**: اضغط على `a` (يتطلب Android Studio).

## 📱 بنية التطبيق وتدفق المستخدم

### بنية التنقل

يستخدم التطبيق بنية تنقل قائمة على علامات التبويب مع أربعة أقسام رئيسية:

#### 1. **علامة تبويب الترجمة** (الرئيسية)
- **الغرض**: واجهة الترجمة الرئيسية.
- **الميزات**:
  - محدد اللغة مع وظيفة التبديل.
  - إدخال نص مع دعم لوحة مفاتيح تيفيناغ.
  - عناصر تحكم الإدخال/الإخراج الصوتي.
  - زر ترجمة الكاميرا.
  - ترجمة فورية بالذكاء الاصطناعي.
  - حفظ سجل الترجمة.

**تدفق المستخدم**:
1. حدد اللغات المصدر والهدف.
2. أدخل النص عن طريق الكتابة أو الصوت أو الكاميرا.
3. انقر على "ترجمة" للحصول على ترجمة مدعومة بالذكاء الاصطناعي.
4. استخدم الإخراج الصوتي لسماع النطق.
5. احفظ في المفضلة أو السجل.

#### 2. **علامة تبويب السجل**
- **الغرض**: إدارة سجل الترجمة والمفضلة.
- **الميزات**:
  - البحث في الترجمات السابقة.
  - التصفية حسب المفضلة.
  - حذف الإدخالات غير المرغوب فيها.
  - إعادة ترجمة أو تعديل الإدخالات السابقة.

**تدفق المستخدم**:
1. تصفح سجل الترجمة الزمني.
2. ابحث عن ترجمات محددة.
3. تبديل مرشح المفضلة.
4. انقر على أي عنصر لسماع النطق.
5. اسحب أو انقر لحذف الإدخالات.

#### 3. **علامة تبويب الطوارئ**
- **الغرض**: الوصول السريع إلى العبارات الحرجة.
- **الميزات**:
  - عبارات طوارئ محملة مسبقًا.
  - فئات طبية، شرطة، واحتياجات أساسية.
  - عبارات مرمزة حسب الأولوية (عالية/متوسطة/منخفضة).
  - إخراج صوتي فوري.
  - معلومات الاتصال بالطوارئ في المغرب.

**تدفق المستخدم**:
1. حدد فئة الطوارئ (طبية، طوارئ، احتياجات أساسية).
2. اختر اللغة الهدف.
3. انقر على أي عبارة للحصول على إخراج صوتي فوري.
4. الوصول إلى أرقام الطوارئ في المغرب (15، 19، 177).

#### 4. **علامة تبويب الحكومة**
- **الغرض**: المصطلحات الرسمية والبرلمانية.
- **الميزات**:
  - عبارات الإجراءات البرلمانية.
  - المصطلحات القانونية والإدارية.
  - معلومات عن الحقوق الدستورية.
  - مصطلحات نظام التعليم.
  - عبارات الخدمات العامة.

**تدفق المستخدم**:
1. حدد الفئة (البرلمان، قانوني، إداري، إلخ).
2. اختر اللغة الهدف.
3. تصفح العبارات الخاصة بالسياق.
4. تعرف على الحقوق اللغوية في المغرب.

### تفاعلات المستخدم الرئيسية

#### اختيار اللغة
- انقر على أزرار اللغة لفتح نافذة الاختيار.
- استخدم زر التبديل لعكس اتجاه الترجمة بسرعة.
- ردود فعل مرئية مع رسوم متحركة سلسة.

#### طرق إدخال النص
1. **الكتابة**: إدخال نص قياسي من لوحة المفاتيح.
2. **لوحة مفاتيح تيفيناغ**: تبديل لوحة مفاتيح تيفيناغ الافتراضية.
3. **الإدخال الصوتي**: انقر على الميكروفون لتحويل الكلام إلى نص.
4. **الكاميرا**: التعرف الضوئي على الحروف (OCR) للنص من الصور.

#### عملية الترجمة
1. أدخل النص باللغة المصدر.
2. انقر على زر "ترجمة" البارز.
3. يظهر مؤشر معالجة الذكاء الاصطناعي التقدم.
4. تظهر النتائج مع خيار النطق.
5. الحفظ التلقائي في السجل.

#### الميزات الصوتية
- **الإدخال**: التعرف على الكلام بلغات متعددة.
- **الإخراج**: تحويل النص إلى كلام بنطق صحيح.
- **كشف اللغة**: تحديد تلقائي للغة.

## 🛠 البنية التقنية

### إطار عمل الواجهة الأمامية
- **Expo SDK 52.0.30**: إطار عمل تطوير متعدد المنصات.
- **React Native**: تطوير تطبيقات محمولة أصلية.
- **Expo Router 4.0.17**: نظام توجيه قائم على الملفات.
- **TypeScript**: تطوير آمن من حيث الأنواع.

### مكتبات واجهة المستخدم/تجربة المستخدم
- **Expo Linear Gradient**: خلفيات متدرجة جميلة.
- **Expo Blur**: تأثيرات زجاجية (Glass-morphism).
- **Lucide React Native**: نظام أيقونات متسق.
- **Expo Google Fonts**: عائلة خطوط Inter.

### الميزات الأساسية
- **Expo Speech**: وظيفة تحويل النص إلى كلام.
- **Expo Camera**: التعرف الضوئي على الحروف وترجمة الصور.
- **Expo Haptics**: ردود فعل لمسية (للجوال فقط).
- **Better SQLite3**: تخزين بيانات محلي.

### تكامل الذكاء الاصطناعي
- **نموذج Gemma-3n 4b**: نموذج غير متصل بالإنترنت مضبوط بدقة للغة تمازيغت (إصدار يونيو 2025).
- **Google Gemini API**: وضع عبر الإنترنت يستخدم أحدث Gemma-3 12b لأعلى دقة.
- **معالجة مزدوجة**: ذكاء اصطناعي سحابي وعلى الجهاز مع تراجع تلقائي.
- **تحسين للطوارئ**: استدلال ذو أولوية للتواصل الحرج.
- **TensorFlow Lite**: نشر محسن على الأجهزة المحمولة لنظامي Android و iOS.

### بنية قاعدة بيانات مزدوجة ثورية
- **🌐 قاعدة بيانات Convex في الوقت الفعلي**: منصة تعاونية قائمة على السحابة.
  - مشاركة الترجمة في الوقت الفعلي بين المستخدمين في جميع أنحاء العالم.
  - نظام بث للطوارئ مع 10 مستويات للأولوية.
  - التحقق من قبل المجتمع ودقة تعتمد على الحشود.
  - الحفاظ على الثقافة مع توثيق التراث الأمازيغي.
  - 7 جداول مع 30 فهرسًا محسنًا للأداء.
- **📱 قاعدة بيانات Expo SQLite**: تخزين محلي يعمل دون اتصال أولاً.
  - وصول فوري لعبارات الطوارئ (يعمل بدون إنترنت).
  - ترجمات مخبأة مسبقًا للموثوقية دون اتصال.
  - قائمة انتظار مزامنة تلقائية لاستعادة الاتصال.
  - استعلامات محلية محسنة للبطارية (زمن استجابة <10 مللي ثانية).

📖 **[وثائق مفصلة لبنية قاعدة البيانات](documentation/DATABASE_ARCHITECTURE.md)**

## 🌍 نظام ترجمة متعدد الاتجاهات

### **اللغات المدعومة**
- **تمازيغت (ⵜⴰⵎⴰⵣⵉⵖⵜ)**: لغة أمازيغية أصلية مع دعم لخط تيفيناغ.
- **العربية (العربية)**: اللغة العربية الفصحى الحديثة (اللغة الرسمية المشاركة في المغرب).
- **الفرنسية (Français)**: لغة إدارية وتعليمية.
- **الإنجليزية**: لغة التواصل الدولي والاستجابة للطوارئ.

### **مصفوفة الترجمة**
**ترجمة متعددة الاتجاهات**: يسهل التطبيق الترجمة بين جميع أزواج اللغات:

| من/إلى | تمازيغت | العربية | الفرنسية | الإنجليزية |
|---------|-----------|--------|--------|---------|
| **تمازيغت** | — | ✅ | ✅ | ✅ |
| **العربية** | ✅ | — | ✅ | ✅ |
| **الفرنسية** | ✅ | ✅ | — | ✅ |
| **الإنجليزية** | ✅ | ✅ | ✅ | — |

### **تنفيذ اللغات على مراحل**
**الإصدار 1.0 (الحالي)**: التركيز على تاشلحيت وحروف تيفيناغ.
**الإصدار 2.0 (المخطط له)**: متغيرات إضافية من تمازيغت (تاريفيت، تمازيغت الأطلس المتوسط) بعد الاختبار الميداني.

يضمن هذا النهج المرحلي الجودة والدقة الثقافية مع بناء أساس لدعم شامل للغات الأمازيغية.

### **الميزات الثقافية واللغوية**
- **دعم الخطوط**: أنظمة الكتابة اللاتينية والعربية وتيفيناغ (ⵜⵉⴼⵉⵏⴰⵖ).
- **الدعم الصوتي**: تسجيلات صوتية أصلية لعبارات الطوارئ.
- **السياق الثقافي**: عبارات مكيفة للسياقات الاجتماعية والإدارية المغربية.
- **التوافق الدستوري**: يدعم حقوق اللغة في المادة 5 من دستور المغرب لعام 2011.

## 🚨 سياق التواصل في حالات الطوارئ

### **الاستجابة لزلزال المغرب 2023**
أبرز الزلزال المدمر الذي ضرب المغرب حواجز تواصل حرجة بين عمال الإنقاذ والمجتمعات الناطقة باللغة الأمازيغية. يعالج هذا التطبيق تلك الفجوات مباشرة من خلال توفير:

- **ترجمة فورية للطوارئ**: لا يتطلب إعدادًا أو تدريبًا.
- **موثوقية دون اتصال**: يعمل عندما تكون شبكات الهاتف المحمول معطلة.
- **حساسية ثقافية**: يحترم التراث اللغوي الأمازيغي مع تمكين الاستجابة للطوارئ.
- **أداة لعمال الإنقاذ**: يتيح التواصل الفعال مع السكان المتضررين.

### **حالات استخدام الطوارئ**
- **الطوارئ الطبية**: "أحتاج مساعدة طبية" ← "ⵔⵉⵖ ⵜⵉⵡⵉⵙⵉ ⵏ ⵓⵙⴳⵏⴼ"
- **البحث والإنقاذ**: "أين أنت؟" ← "ⵎⴰⵏⵉ ⵜⵍⵍⵉⴷ?"
- **الاحتياجات الأساسية**: "ماء" ← "ⴰⵎⴰⵏ"، "طعام" ← "ⵓⵛⵛⵉ"
- **خدمات الموقع**: "مستشفى" ← "ⴰⵙⴳⵏⴼ"، "شرطة" ← "ⵍⴱⵓⵍⵉⵙ"

## 📋 سكربتات التطوير

```bash
# التطوير
npm run dev          # بدء خادم التطوير
pnpm dev            # البدء باستخدام pnpm

# البناء
npm run build:web   # البناء للنشر على الويب
pnpm build:web      # البناء باستخدام pnpm

# جودة الكود
npm run lint        # تشغيل ESLint
pnpm lint          # تشغيل lint باستخدام pnpm
```

## 🔧 الإعداد

### متغيرات البيئة
أنشئ ملف `.env` في الدليل الجذر:

```env
# إعدادات Google Gemini API (للوضع عبر الإنترنت)
EXPO_PUBLIC_GEMINI_API_KEY=your-gemini-api-key-here

# إعدادات التطبيق
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_API_URL=https://your-api-url.com
EXPO_PUBLIC_GEMMA_MODEL_PATH=./models/gemma-3
```

احصل على مفتاح Gemini API المجاني من: https://ai.google.dev/

### الميزات الخاصة بالمنصة
يكتشف التطبيق المنصة تلقائيًا ويمكّن/يعطّل الميزات:

- **الويب**: وظائف كاملة باستثناء ردود الفعل اللمسية وبعض الميزات الأصلية.
- **iOS/Android**: مجموعة ميزات كاملة بما في ذلك ردود الفعل اللمسية وواجهات برمجة التطبيقات الأصلية.
- **متجاوب**: يتكيف مع أحجام الشاشات والتوجهات المختلفة.

## 🚀 النشر

### النشر على الويب
```bash
npm run build:web
# انشر مجلد dist إلى مزود الاستضافة الخاص بك
```

### متاجر تطبيقات الجوال
1. **متجر تطبيقات iOS**: استخدم EAS Build للنشر على iOS.
2. **متجر Google Play**: استخدم EAS Build للنشر على Android.
3. **تحديثات Expo**: تحديثات عبر الهواء للتطبيقات المنشورة.

### EAS Build (موصى به)```bash
# تثبيت EAS CLI
npm install -g @expo/eas-cli

# إعداد EAS
eas build:configure

# البناء للإنتاج
eas build --platform all
```

## 🤝 المساهمة

1. قم بعمل نسخة (fork) من المستودع.
2. أنشئ فرعًا للميزة (`git checkout -b feature/amazing-feature`).
3. قم بتثبيت (commit) تغييراتك (`git commit -m 'Add amazing feature'`).
4. ادفع (push) إلى الفرع (`git push origin feature/amazing-feature`).
5. افتح طلب سحب (Pull Request).

## 📄 الترخيص

هذا المشروع مرخص بموجب ترخيص MIT - انظر ملف LICENSE للحصول على التفاصيل.

### **الشركاء التقنيون**
- **Google DeepMind**: لنموذج Gemma-3n وفرصة هاكاثون Kaggle.
- **مجتمع لغة تمازيغت**: للتوجيه والتحقق الثقافي واللغوي.
- **المعهد الملكي للثقافة الأمازيغية (IRCAM) في المغرب**: لمعايير خط تيفيناغ.

### **إطار عمل التطوير**
- **فريق Expo**: لإطار عمل التطوير متعدد المنصات الممتاز.
- **مجتمع React Native**: لأدوات تطوير تطبيقات الجوال.
- **Google Fonts**: لعائلة خطوط Inter الجميلة.
- **Lucide Icons**: لمكتبة الأيقونات الشاملة.